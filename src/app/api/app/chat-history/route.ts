

import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CHAT_BASE_PATH = path.join(process.cwd(), 'data', 'app', 'chat-history');

function ensureUserDir(userId: string) {
  const dir = path.join(CHAT_BASE_PATH, userId);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return dir;
}

export async function GET(req: NextRequest) {
  const userId = req.nextUrl.searchParams.get('userId');
  if (!userId) {
    return NextResponse.json({ error: 'Missing userId' }, { status: 400 });
  }

  const userDir = path.join(CHAT_BASE_PATH, userId);
  if (!fs.existsSync(userDir)) {
    return NextResponse.json([]);
  }

  const files = fs.readdirSync(userDir).filter(f => f.endsWith('.json'));
  const sessions = files.map(file => {
    const content = fs.readFileSync(path.join(userDir, file), 'utf-8');
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  }).filter(Boolean);

  return NextResponse.json(sessions);
}

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { userId, prompt, messages } = body;

  if (!userId || !prompt || !messages) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
  }

  const userDir = ensureUserDir(String(userId));
  const sessionFiles = fs.readdirSync(userDir).filter(f => f.endsWith('.json'));
  const sessionId = sessionFiles.length + 1;

  const session = {
    sessionId,
    userId,
    prompt,
    timestamp: new Date().toISOString(),
    messages
  };

  const filePath = path.join(userDir, `${sessionId}.json`);
  fs.writeFileSync(filePath, JSON.stringify(session, null, 2));

  return NextResponse.json({ success: true, session });
}

export async function DELETE(req: NextRequest) {
  const body = await req.json();
  const { userId, sessionId } = body;

  if (!userId || !sessionId) {
    return NextResponse.json({ error: 'Missing userId or sessionId' }, { status: 400 });
  }

  const filePath = path.join(CHAT_BASE_PATH, String(userId), `${sessionId}.json`);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    return NextResponse.json({ success: true });
  } else {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 });
  }
}